{"name": "vue-project", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"element-plus": "^2.10.4", "pdfjs-dist": "^5.4.54", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.31.0", "@prettier/plugin-oxc": "^0.0.4", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "eslint": "^9.31.0", "eslint-plugin-oxlint": "~1.8.0", "eslint-plugin-vue": "~10.3.0", "globals": "^16.3.0", "npm-run-all2": "^8.0.4", "oxlint": "~1.8.0", "prettier": "3.6.2", "vite": "npm:rolldown-vite@latest", "vite-plugin-vue-devtools": "^8.0.0"}}